import { render, screen } from 'tests/test-utils';
import { IssueQualityIndicator } from './IssueQualityIndicator';

describe('<IssueQualityIndicator />', () => {
  describe("when qualityScore is null", () => {
    it('renders nothing', () => {
      render(<IssueQualityIndicator qualityScore={null} />);

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  })

  describe("when qualityScore is > 30", () => {
    it('renders an empty div', () => {
      const { container } = render(<IssueQualityIndicator qualityScore={35} className='test-class' />);

      expect(container.firstChild).toHaveClass('test-class');
      expect(container.firstChild).toBeEmptyDOMElement();
    });
  })

  describe("when qualityScore is < 30", () => {
    describe("and < 20", () => {
      it('renders a progress bar with the correct color', () => {
        render(<IssueQualityIndicator qualityScore={15} />);

        
      });
    });
    describe("and >= 20", () => { });
  })
});